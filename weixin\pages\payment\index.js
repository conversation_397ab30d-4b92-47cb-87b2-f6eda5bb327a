const { billApi, paymentApi } = require('../../api/index.js');

Page({
  data: {
    billInfo: {},
    selectedPayment: 'wechat',
    selectedFeeType: 'heating', // 'heating' 用热缴费(全额), 'maintenance' 不用热(管网维护费)
    isAgreed: true,
    loading: false,
    billId: null,
    heatingYear: null,
    fromPage: 'index' // 'index' 从首页进入, 'bill' 从账单页进入
  },

  onLoad(options) {
    console.log('缴费页面参数:', options);

    const billId = options.billId;
    const heatingYear = options.heatingYear ? parseInt(options.heatingYear) : null;
    const fromPage = options.from || 'index'; // 默认从首页进入

    this.setData({
      billId: billId,
      heatingYear: heatingYear,
      fromPage: fromPage
    });

    if (billId) {
      // 从账单页进入，加载指定账单信息
      this.loadBillInfo(billId);
    } else if (heatingYear) {
      // 从首页进入，但指定了年度
      this.loadBillByYear(heatingYear);
    } else {
      // 从首页进入，获取当前供暖季账单
      this.loadCurrentBill();
    }
  },

  /**
   * 加载指定账单信息（从账单页进入）
   */
  loadBillInfo(billId) {
    const userInfo = wx.getStorageSync('userInfo');
    if (!userInfo || !userInfo.houseId) {
      wx.showModal({
        title: '提示',
        content: '请先绑定户号',
        showCancel: false,
        success: () => {
          wx.navigateBack();
        }
      });
      return;
    }

    this.setData({ loading: true });
    wx.showLoading({
      title: '加载账单中...'
    });

    // 先获取账单基本信息，获取供暖年度
    billApi.getBillDetail(billId).then(res => {
      console.log('获取账单基本信息响应:', res);

      if (res && res.data && res.data.heatYear) {
        const heatingYear = res.data.heatYear;
        this.setData({ heatingYear: heatingYear });

        // 使用简化账单接口获取完整信息
        return billApi.getSimpleBillInfo({
          houseId: userInfo.houseId,
          heatingYear: heatingYear
        });
      } else {
        throw new Error('无法获取账单的供暖年度信息');
      }
    }).then(res => {
      this.processBillData(res);
    }).catch(err => {
      wx.hideLoading();
      this.setData({ loading: false });
      console.error('获取账单信息失败:', err);
      wx.showToast({
        title: err.message || '获取账单失败',
        icon: 'none'
      });
    });
  },

  /**
   * 按年度加载账单信息（从首页进入，指定年度）
   */
  loadBillByYear(heatingYear) {
    const userInfo = wx.getStorageSync('userInfo');
    if (!userInfo || !userInfo.houseId) {
      wx.showModal({
        title: '提示',
        content: '请先绑定户号',
        showCancel: false,
        success: () => {
          wx.navigateBack();
        }
      });
      return;
    }

    this.setData({ loading: true });
    wx.showLoading({
      title: '加载账单中...'
    });

    // 使用简化账单接口
    billApi.getSimpleBillInfo({
      houseId: userInfo.houseId,
      heatingYear: heatingYear
    }).then(res => {
      this.processBillData(res);
    }).catch(err => {
      wx.hideLoading();
      this.setData({ loading: false });
      console.error('获取账单信息失败:', err);
      wx.showToast({
        title: err.message || '获取账单失败',
        icon: 'none'
      });
    });
  },

  /**
   * 处理账单数据响应
   */
  processBillData(res) {
    wx.hideLoading();
    this.setData({ loading: false });
    console.log('简化账单信息响应:', res);

    if (res && res.code === 200 && res.data) {
      const data = res.data;

      // 检查是否有账单数据
      if (data.billId && data.paymentStatusInfo && data.paymentStatusInfo.paymentStatus !== 'no_bill') {
        // 有账单数据
        // 计算管网维护费（用热费的30%）
        const maintenanceFee = (data.billFeeInfo.heatingFee * 0.3).toFixed(2);

        this.setData({
          billId: data.billId,
          billInfo: {
            billId: data.billId,
            houseNumber: data.houseInfo.houseNumber,
            address: data.houseInfo.address,
            area: data.houseInfo.area,
            heatingYear: data.houseInfo.heatingYear,
            heatingStatusText: data.houseInfo.heatingStatusText,
            isHeating: data.houseInfo.isHeating,
            // 费用信息
            heatingFee: data.billFeeInfo.heatingFee,
            feeTypeName: data.billFeeInfo.feeTypeName,
            unitPrice: data.billFeeInfo.unitPrice,
            overdueAmount: data.billFeeInfo.overdueAmount,
            totalPayableAmount: data.billFeeInfo.totalPayableAmount,
            actualPaidAmount: data.billFeeInfo.actualPaidAmount,
            maintenanceFee: maintenanceFee, // 添加管网维护费
            // 状态信息
            status: data.paymentStatusInfo.paymentStatus,
            statusText: data.paymentStatusInfo.paymentStatusText,
            remainingAmount: data.paymentStatusInfo.remainingAmount
          }
        });

        // 根据房屋用热状态设置默认缴费类型
        const defaultFeeType = data.houseInfo.isHeating === 1 ? 'heating' : 'maintenance';
        this.setData({ selectedFeeType: defaultFeeType });

      } else {
        // 没有账单数据
        wx.showModal({
          title: '提示',
          content: res.message || '暂无账单信息',
          showCancel: false,
          success: () => {
            wx.navigateBack();
          }
        });
      }
    } else {
      wx.showToast({
        title: res ? res.message : '获取账单失败',
        icon: 'none'
      });
    }
  },

  /**
   * 获取当前供暖年度
   */
  getCurrentHeatingYear() {
    const now = new Date();
    const currentYear = now.getFullYear();
    const currentMonth = now.getMonth() + 1;

    // 如果是1-10月，则属于上一年的供暖年度
    if (currentMonth <= 6) {
      return currentYear - 1;
    } else {
      // 如果是11-12月，则属于当年的供暖年度
      return currentYear;
    }
  },

  /**
   * 加载当前供暖季账单（从首页进入）
   */
  loadCurrentBill() {
    const currentYear = this.getCurrentHeatingYear();
    this.setData({ heatingYear: currentYear });
    this.loadBillByYear(currentYear);
  },

  /**
   * 选择缴费类型
   */
  selectFeeType(e) {
    const type = e.currentTarget.dataset.type;
    this.setData({
      selectedFeeType: type
    });

    const typeName = type === 'heating' ? '用热缴费(全额)' : '不用热(管网维护费)';
    wx.showToast({
      title: `已选择${typeName}`,
      icon: 'none',
      duration: 1000
    });
  },

  /**
   * 选择支付方式
   */
  selectPayment(e) {
    const type = e.currentTarget.dataset.type;
    this.setData({
      selectedPayment: type
    });

    wx.showToast({
      title: `已选择${type === 'wechat' ? '微信支付' : '支付宝'}`,
      icon: 'none',
      duration: 1000
    });
  },

  /**
   * 获取当前选择的缴费金额
   */
  getCurrentPaymentAmount() {
    const { billInfo, selectedFeeType } = this.data;

    if (selectedFeeType === 'heating') {
      // 用热缴费(全额)：应缴费金额
      return billInfo.totalPayableAmount || billInfo.heatingFee || 0;
    } else {
      // 不用热(管网维护费)：使用预计算的管网维护费
      return parseFloat(billInfo.maintenanceFee) || 0;
    }
  },

  toggleAgreement() {
    this.setData({
      isAgreed: !this.data.isAgreed
    });
  },

  viewAgreement() {
    wx.showModal({
      title: '供热缴费协议',
      content: '1. 缴费成功后不可退款\n2. 请确认账单信息无误\n3. 如有疑问请联系客服\n4. 缴费后请保留支付凭证',
      showCancel: true,
      cancelText: '关闭',
      confirmText: '我知道了'
    });
  },

  handlePayment() {
    if (!this.data.isAgreed) {
      wx.showToast({
        title: '请先同意缴费协议',
        icon: 'none'
      });
      return;
    }

    if (!this.data.selectedPayment) {
      wx.showToast({
        title: '请选择支付方式',
        icon: 'none'
      });
      return;
    }

    if (!this.data.billId) {
      wx.showToast({
        title: '账单信息异常',
        icon: 'none'
      });
      return;
    }

    const paymentAmount = this.getCurrentPaymentAmount();
    const feeTypeName = this.data.selectedFeeType === 'heating' ? '用热缴费(全额)' : '不用热(管网维护费)';

    wx.showModal({
      title: '确认支付',
      content: `确认支付 ${feeTypeName} ¥${paymentAmount.toFixed(2)} 吗？`,
      success: (res) => {
        if (res.confirm) {
          this.processPayment();
        }
      }
    });
  },

  processPayment() {
    const { selectedPayment } = this.data;
    const userInfo = wx.getStorageSync('userInfo');

    if (!userInfo || !userInfo.houseId) {
      wx.showToast({
        title: '请先绑定户号',
        icon: 'none'
      });
      return;
    }

    wx.showLoading({
      title: '正在调起支付...'
    });

    // 模拟支付成功，然后直接调用在线支付接口
    setTimeout(() => {
      wx.hideLoading();

      // 模拟支付成功率90%
      const paymentSuccess = Math.random() > 0.1;

      if (paymentSuccess) {
        // 支付成功后，调用后端在线支付接口记录缴费
        this.recordOnlinePayment(selectedPayment);
      } else {
        this.handlePaymentFailure('支付失败，请重试');
      }
    }, 2000);
  },

  // 记录在线支付
  recordOnlinePayment(paymentMethod = 'wechat') {
    const userInfo = wx.getStorageSync('userInfo');
    const { billId, billInfo, selectedFeeType } = this.data;

    console.log('开始记录在线支付');
    console.log('userInfo:', userInfo);
    console.log('billId:', billId, 'type:', typeof billId);
    console.log('billInfo:', billInfo);
    console.log('selectedFeeType:', selectedFeeType);

    // 数据验证
    if (!billId) {
      wx.showToast({
        title: '账单ID不能为空',
        icon: 'none'
      });
      return;
    }

    if (!userInfo || !userInfo.houseId) {
      wx.showToast({
        title: '请先绑定户号',
        icon: 'none'
      });
      return;
    }

    if (!billInfo) {
      wx.showToast({
        title: '账单信息异常',
        icon: 'none'
      });
      return;
    }

    wx.showLoading({
      title: '正在处理缴费记录...'
    });

    // 生成交易号
    const transactionPrefix = paymentMethod === 'wechat' ? 'WX' : 'ALI';
    const transactionNo = transactionPrefix + Date.now() + Math.floor(Math.random() * 1000);

    // 获取缴费金额
    const paymentAmount = this.getCurrentPaymentAmount();

    // 确定用热状态
    const isHeating = selectedFeeType === 'heating' ? 1 : 0;

    const paymentData = {
      billId: parseInt(billId),
      houseId: parseInt(userInfo.houseId),
      amount: parseFloat(paymentAmount),
      paymentMethod: paymentMethod,
      transactionNo: transactionNo,
      isHeating: isHeating, // 添加用热状态
      feeType: selectedFeeType, // 添加缴费类型
      remark: `微信小程序${paymentMethod === 'wechat' ? '微信' : '支付宝'}在线缴费 - ${selectedFeeType === 'heating' ? '用热缴费(全额)' : '不用热(管网维护费)'}`
    };
    console.log('调用在线支付接口，参数:', paymentData);
    paymentApi.submitPayment(paymentData).then(res => {
      wx.hideLoading();
      console.log('在线支付接口响应:', res);

      if (res.code === 200) {
        console.log('缴费成功，准备跳转到成功页面');

        // 获取实际缴费金额
        const actualPaymentAmount = this.getCurrentPaymentAmount();

        console.log('跳转参数:', {
          amount: actualPaymentAmount,
          paymentId: res.data.paymentId || res.data.id,
          billId: billId
        });

        wx.showToast({
          title: '缴费成功',
          icon: 'success',
          duration: 1500
        });

        // 缩短延迟时间，立即跳转
        setTimeout(() => {
          const paymentId = res.data.paymentId || res.data.id || '';
          wx.redirectTo({
            url: `/pages/payment/success?amount=${actualPaymentAmount}&paymentId=${paymentId}&billId=${billId}`,
            success: () => {
              console.log('跳转成功页面成功');
            },
            fail: (err) => {
              console.error('跳转成功页面失败:', err);
              // 如果跳转失败，尝试返回首页
              wx.showModal({
                title: '缴费成功',
                content: '缴费已完成，点击确定返回首页',
                showCancel: false,
                success: () => {
                  wx.switchTab({
                    url: '/pages/index/index'
                  });
                }
              });
            }
          });
        }, 1500);
      } else {
        this.handlePaymentFailure(res.message || '缴费记录处理失败');
      }
    }).catch(err => {
      wx.hideLoading();
      console.error('在线缴费记录失败:', err);

      // 支付接口调用失败的处理
      wx.showModal({
        title: '缴费处理失败',
        content: err.message || '网络异常，请稍后重试',
        showCancel: true,
        cancelText: '返回',
        confirmText: '重试',
        success: (res) => {
          if (res.confirm) {
            // 重试支付
            this.processPayment();
          } else {
            // 返回上一页
            wx.navigateBack();
          }
        }
      });
    });
  },

  wechatPay(paymentOrder) {
    // 这个方法现在不再使用，支付逻辑已移到 processPayment 中
    console.log('wechatPay method called but not used');
  },

  alipayPay(paymentOrder) {
    // 这个方法现在不再使用，支付逻辑已移到 processPayment 中
    console.log('alipayPay method called but not used');
  },

  handlePaymentFailure(message) {
    wx.showModal({
      title: '支付失败',
      content: message,
      showCancel: true,
      cancelText: '取消',
      confirmText: '重试',
      success: (res) => {
        if (res.confirm) {
          this.processPayment();
        }
      }
    });
  }
});



