2025-08-19T08:20:44.427+08:00  INFO 21596 --- [main] com.heating.HeatingApplication           : Starting HeatingApplication using Java 21.0.7 with PID 21596 (E:\taibo_company\tb_project\tbkj_hot_engine_cloud\4-Source\app\backend\target\classes started by A in E:\taibo_company\tb_project\tbkj_hot_engine_cloud\4-Source\app\backend)
2025-08-19T08:20:44.450+08:00 DEBUG 21596 --- [main] com.heating.HeatingApplication           : Running with Spring Boot v3.2.3, Spring v6.1.4
2025-08-19T08:20:44.450+08:00  INFO 21596 --- [main] com.heating.HeatingApplication           : The following 1 profile is active: "test"
2025-08-19T08:20:50.591+08:00  INFO 21596 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-19T08:20:51.110+08:00  INFO 21596 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 322 ms. Found 42 JPA repository interfaces.
2025-08-19T08:20:52.512+08:00  INFO 21596 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 8889 (http)
2025-08-19T08:20:52.526+08:00  INFO 21596 --- [main] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-08-19T08:20:52.526+08:00  INFO 21596 --- [main] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.19]
2025-08-19T08:20:52.638+08:00  INFO 21596 --- [main] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-08-19T08:20:52.639+08:00  INFO 21596 --- [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 8089 ms
2025-08-19T08:20:52.789+08:00 DEBUG 21596 --- [main] c.h.filter.JwtAuthenticationFilter       : Filter 'jwtAuthenticationFilter' configured for use
2025-08-19T08:20:53.223+08:00  INFO 21596 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-08-19T08:20:53.392+08:00  INFO 21596 --- [main] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 6.4.4.Final
2025-08-19T08:20:53.485+08:00  INFO 21596 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-08-19T08:20:53.687+08:00  INFO 21596 --- [main] com.zaxxer.hikari.HikariDataSource       : MyHikariPool - Starting...
2025-08-19T08:20:54.736+08:00  INFO 21596 --- [main] com.zaxxer.hikari.pool.HikariPool        : MyHikariPool - Added connection com.mysql.cj.jdbc.ConnectionImpl@59b98ad1
2025-08-19T08:20:54.736+08:00  INFO 21596 --- [main] com.zaxxer.hikari.HikariDataSource       : MyHikariPool - Start completed.
2025-08-19T08:20:55.356+08:00  INFO 21596 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-08-19T08:20:57.305+08:00  INFO 21596 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-08-19T08:20:57.840+08:00  INFO 21596 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-08-19T08:20:58.353+08:00  INFO 21596 --- [main] o.s.d.j.r.query.QueryEnhancerFactory     : Hibernate is in classpath; If applicable, HQL parser will be used.
2025-08-19T08:21:00.860+08:00  INFO 21596 --- [main] com.heating.config.WebMvcConfig          : 配置静态资源跨域访问: /uploads/**
2025-08-19T08:21:01.040+08:00  INFO 21596 --- [main] com.heating.config.WebMvcConfig          : 配置静态资源映射: /uploads/** -> file:/root/project/tbkj/web/uploads/
2025-08-19T08:21:01.089+08:00  INFO 21596 --- [main] o.s.s.web.DefaultSecurityFilterChain     : Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@279a3298, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@42e1aecb, org.springframework.security.web.context.SecurityContextHolderFilter@5fd01e54, org.springframework.security.web.header.HeaderWriterFilter@69fcced9, org.springframework.web.filter.CorsFilter@fa230d1, org.springframework.security.web.authentication.logout.LogoutFilter@4f80d322, com.heating.filter.JwtAuthenticationFilter@20256a0b, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@7de72197, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@4145bb9f, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@608019af, org.springframework.security.web.access.ExceptionTranslationFilter@67481b2b, org.springframework.security.web.access.intercept.AuthorizationFilter@6aa658c3]
2025-08-19T08:21:01.573+08:00  INFO 21596 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port 8889 (http) with context path ''
2025-08-19T08:21:01.581+08:00  INFO 21596 --- [main] com.heating.HeatingApplication           : Started HeatingApplication in 18.308 seconds (process running for 20.424)
2025-08-19T08:23:12.798+08:00  INFO 21596 --- [http-nio-8889-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-19T08:23:12.799+08:00  INFO 21596 --- [http-nio-8889-exec-1] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-08-19T08:23:12.801+08:00  INFO 21596 --- [http-nio-8889-exec-1] o.s.web.servlet.DispatcherServlet        : Completed initialization in 2 ms
2025-08-19T08:23:12.826+08:00 DEBUG 21596 --- [http-nio-8889-exec-1] o.s.security.web.FilterChainProxy        : Securing POST /api/weixin/bill/simple-info
2025-08-19T08:23:12.860+08:00 DEBUG 21596 --- [http-nio-8889-exec-1] o.s.security.web.FilterChainProxy        : Secured POST /api/weixin/bill/simple-info
2025-08-19T08:23:12.972+08:00  INFO 21596 --- [http-nio-8889-exec-1] c.heating.controller.WeixinController    : 收到获取简化账单信息请求: SimpleBillInfoRequest(houseId=7, heatingYear=2025)
2025-08-19T08:23:12.973+08:00  INFO 21596 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 开始获取简化账单信息: SimpleBillInfoRequest(houseId=7, heatingYear=2025)
2025-08-19T08:23:13.372+08:00  INFO 21596 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 房屋信息: ID=7, 户号=HT2024000121, 用热状态=0
2025-08-19T08:23:13.372+08:00  INFO 21596 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 查询供暖年度: 2025
2025-08-19T08:23:13.520+08:00  INFO 21596 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 构建房屋信息
2025-08-19T08:23:13.521+08:00  INFO 21596 --- [http-nio-8889-exec-1] c.h.service.impl.HeatUnitServiceImpl     : 根据住户id获取小区名字: houseId=7
2025-08-19T08:23:13.909+08:00  INFO 21596 --- [http-nio-8889-exec-1] c.h.service.impl.HeatUnitServiceImpl     : 成功获取小区名字: houseId=7, heatUnitId=1, communityName=印象小区
2025-08-19T08:23:13.909+08:00  INFO 21596 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 房屋信息构建完成: 用热状态=不供暖
2025-08-19T08:23:13.909+08:00  INFO 21596 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 账单信息: ID=7, 总金额=3365.17, 已缴金额=1009.55, 欠费金额=0.00, 状态=partial_paid
2025-08-19T08:23:13.910+08:00  INFO 21596 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 构建账单费用信息，用热状态: 0
2025-08-19T08:23:13.945+08:00  INFO 21596 --- [http-nio-8889-exec-1] c.h.s.impl.HeatingFeeRuleServiceImpl     : 获取到规则ID=1的单价: 5.80
2025-08-19T08:23:13.945+08:00  INFO 21596 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 单价: 5.80 元/㎡
2025-08-19T08:23:13.945+08:00  INFO 21596 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 计算管网维护费，房屋面积: 116.04, 计费规则ID: 1
2025-08-19T08:23:14.139+08:00  INFO 21596 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 计费规则: 最低缴费比例=0.30
2025-08-19T08:23:14.140+08:00  INFO 21596 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 管网维护费计算完成: 1009.5510 元 (账单金额 3365.17 * 最低比例 0.30)
2025-08-19T08:23:14.140+08:00  INFO 21596 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 不用热状态 - 管网维护费: 1009.5510 元
2025-08-19T08:23:14.140+08:00  INFO 21596 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 欠费金额: 0.00 元
2025-08-19T08:23:14.140+08:00  INFO 21596 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 应缴费金额: 1009.5510 元
2025-08-19T08:23:14.140+08:00  INFO 21596 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 实际缴费金额: 1009.55 元
2025-08-19T08:23:14.140+08:00  INFO 21596 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 构建缴费状态信息，账单状态: partial_paid
2025-08-19T08:23:14.140+08:00  INFO 21596 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 缴费状态信息构建完成: 状态=部分缴费, 显示实际缴费=true, 剩余金额=2355.62
2025-08-19T08:23:14.140+08:00  INFO 21596 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 构建缴费记录列表，账单ID: 7
2025-08-19T08:23:14.185+08:00  INFO 21596 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 找到缴费记录数量: 1
2025-08-19T08:23:14.186+08:00  INFO 21596 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 缴费记录: ID=12, 金额=1009.55, 方式=微信支付, 日期=2025-08-14 09:10
2025-08-19T08:23:14.186+08:00  INFO 21596 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 缴费记录构建完成，共 1 条记录
2025-08-19T08:23:14.186+08:00  INFO 21596 --- [http-nio-8889-exec-1] c.heating.service.impl.BillServiceImpl   : 简化账单信息获取成功
2025-08-19T08:23:14.186+08:00  INFO 21596 --- [http-nio-8889-exec-1] c.heating.controller.WeixinController    : 简化账单信息获取完成: code=200, message=获取账单信息成功
2025-08-19T08:23:14.226+08:00 DEBUG 21596 --- [http-nio-8889-exec-1] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-19T08:23:20.976+08:00 DEBUG 21596 --- [http-nio-8889-exec-4] o.s.security.web.FilterChainProxy        : Securing POST /api/weixin/bill/simple-info
2025-08-19T08:23:20.977+08:00 DEBUG 21596 --- [http-nio-8889-exec-4] o.s.security.web.FilterChainProxy        : Secured POST /api/weixin/bill/simple-info
2025-08-19T08:23:20.978+08:00 DEBUG 21596 --- [http-nio-8889-exec-7] o.s.security.web.FilterChainProxy        : Securing POST /api/weixin/bill/simple-info
2025-08-19T08:23:20.978+08:00  INFO 21596 --- [http-nio-8889-exec-4] c.heating.controller.WeixinController    : 收到获取简化账单信息请求: SimpleBillInfoRequest(houseId=7, heatingYear=2025)
2025-08-19T08:23:20.978+08:00  INFO 21596 --- [http-nio-8889-exec-4] c.heating.service.impl.BillServiceImpl   : 开始获取简化账单信息: SimpleBillInfoRequest(houseId=7, heatingYear=2025)
2025-08-19T08:23:20.978+08:00 DEBUG 21596 --- [http-nio-8889-exec-7] o.s.security.web.FilterChainProxy        : Secured POST /api/weixin/bill/simple-info
2025-08-19T08:23:20.979+08:00  INFO 21596 --- [http-nio-8889-exec-7] c.heating.controller.WeixinController    : 收到获取简化账单信息请求: SimpleBillInfoRequest(houseId=7, heatingYear=2025)
2025-08-19T08:23:20.979+08:00  INFO 21596 --- [http-nio-8889-exec-7] c.heating.service.impl.BillServiceImpl   : 开始获取简化账单信息: SimpleBillInfoRequest(houseId=7, heatingYear=2025)
2025-08-19T08:23:21.204+08:00  INFO 21596 --- [http-nio-8889-exec-4] c.heating.service.impl.BillServiceImpl   : 房屋信息: ID=7, 户号=HT2024000121, 用热状态=0
2025-08-19T08:23:21.204+08:00  INFO 21596 --- [http-nio-8889-exec-4] c.heating.service.impl.BillServiceImpl   : 查询供暖年度: 2025
2025-08-19T08:23:21.214+08:00  INFO 21596 --- [http-nio-8889-exec-7] c.heating.service.impl.BillServiceImpl   : 房屋信息: ID=7, 户号=HT2024000121, 用热状态=0
2025-08-19T08:23:21.214+08:00  INFO 21596 --- [http-nio-8889-exec-7] c.heating.service.impl.BillServiceImpl   : 查询供暖年度: 2025
2025-08-19T08:23:21.239+08:00  INFO 21596 --- [http-nio-8889-exec-4] c.heating.service.impl.BillServiceImpl   : 构建房屋信息
2025-08-19T08:23:21.239+08:00  INFO 21596 --- [http-nio-8889-exec-4] c.h.service.impl.HeatUnitServiceImpl     : 根据住户id获取小区名字: houseId=7
2025-08-19T08:23:21.249+08:00  INFO 21596 --- [http-nio-8889-exec-7] c.heating.service.impl.BillServiceImpl   : 构建房屋信息
2025-08-19T08:23:21.250+08:00  INFO 21596 --- [http-nio-8889-exec-7] c.h.service.impl.HeatUnitServiceImpl     : 根据住户id获取小区名字: houseId=7
2025-08-19T08:23:21.627+08:00  INFO 21596 --- [http-nio-8889-exec-4] c.h.service.impl.HeatUnitServiceImpl     : 成功获取小区名字: houseId=7, heatUnitId=1, communityName=印象小区
2025-08-19T08:23:21.627+08:00  INFO 21596 --- [http-nio-8889-exec-4] c.heating.service.impl.BillServiceImpl   : 房屋信息构建完成: 用热状态=不供暖
2025-08-19T08:23:21.627+08:00  INFO 21596 --- [http-nio-8889-exec-4] c.heating.service.impl.BillServiceImpl   : 账单信息: ID=7, 总金额=3365.17, 已缴金额=1009.55, 欠费金额=0.00, 状态=partial_paid
2025-08-19T08:23:21.627+08:00  INFO 21596 --- [http-nio-8889-exec-4] c.heating.service.impl.BillServiceImpl   : 构建账单费用信息，用热状态: 0
2025-08-19T08:23:21.654+08:00  INFO 21596 --- [http-nio-8889-exec-7] c.h.service.impl.HeatUnitServiceImpl     : 成功获取小区名字: houseId=7, heatUnitId=1, communityName=印象小区
2025-08-19T08:23:21.654+08:00  INFO 21596 --- [http-nio-8889-exec-7] c.heating.service.impl.BillServiceImpl   : 房屋信息构建完成: 用热状态=不供暖
2025-08-19T08:23:21.654+08:00  INFO 21596 --- [http-nio-8889-exec-7] c.heating.service.impl.BillServiceImpl   : 账单信息: ID=7, 总金额=3365.17, 已缴金额=1009.55, 欠费金额=0.00, 状态=partial_paid
2025-08-19T08:23:21.654+08:00  INFO 21596 --- [http-nio-8889-exec-7] c.heating.service.impl.BillServiceImpl   : 构建账单费用信息，用热状态: 0
2025-08-19T08:23:21.661+08:00  INFO 21596 --- [http-nio-8889-exec-4] c.h.s.impl.HeatingFeeRuleServiceImpl     : 获取到规则ID=1的单价: 5.80
2025-08-19T08:23:21.661+08:00  INFO 21596 --- [http-nio-8889-exec-4] c.heating.service.impl.BillServiceImpl   : 单价: 5.80 元/㎡
2025-08-19T08:23:21.661+08:00  INFO 21596 --- [http-nio-8889-exec-4] c.heating.service.impl.BillServiceImpl   : 计算管网维护费，房屋面积: 116.04, 计费规则ID: 1
2025-08-19T08:23:21.690+08:00  INFO 21596 --- [http-nio-8889-exec-7] c.h.s.impl.HeatingFeeRuleServiceImpl     : 获取到规则ID=1的单价: 5.80
2025-08-19T08:23:21.690+08:00  INFO 21596 --- [http-nio-8889-exec-7] c.heating.service.impl.BillServiceImpl   : 单价: 5.80 元/㎡
2025-08-19T08:23:21.690+08:00  INFO 21596 --- [http-nio-8889-exec-7] c.heating.service.impl.BillServiceImpl   : 计算管网维护费，房屋面积: 116.04, 计费规则ID: 1
2025-08-19T08:23:21.854+08:00  INFO 21596 --- [http-nio-8889-exec-4] c.heating.service.impl.BillServiceImpl   : 计费规则: 最低缴费比例=0.30
2025-08-19T08:23:21.854+08:00  INFO 21596 --- [http-nio-8889-exec-4] c.heating.service.impl.BillServiceImpl   : 管网维护费计算完成: 1009.5510 元 (账单金额 3365.17 * 最低比例 0.30)
2025-08-19T08:23:21.854+08:00  INFO 21596 --- [http-nio-8889-exec-4] c.heating.service.impl.BillServiceImpl   : 不用热状态 - 管网维护费: 1009.5510 元
2025-08-19T08:23:21.854+08:00  INFO 21596 --- [http-nio-8889-exec-4] c.heating.service.impl.BillServiceImpl   : 欠费金额: 0.00 元
2025-08-19T08:23:21.854+08:00  INFO 21596 --- [http-nio-8889-exec-4] c.heating.service.impl.BillServiceImpl   : 应缴费金额: 1009.5510 元
2025-08-19T08:23:21.854+08:00  INFO 21596 --- [http-nio-8889-exec-4] c.heating.service.impl.BillServiceImpl   : 实际缴费金额: 1009.55 元
2025-08-19T08:23:21.854+08:00  INFO 21596 --- [http-nio-8889-exec-4] c.heating.service.impl.BillServiceImpl   : 构建缴费状态信息，账单状态: partial_paid
2025-08-19T08:23:21.855+08:00  INFO 21596 --- [http-nio-8889-exec-4] c.heating.service.impl.BillServiceImpl   : 缴费状态信息构建完成: 状态=部分缴费, 显示实际缴费=true, 剩余金额=2355.62
2025-08-19T08:23:21.855+08:00  INFO 21596 --- [http-nio-8889-exec-4] c.heating.service.impl.BillServiceImpl   : 构建缴费记录列表，账单ID: 7
2025-08-19T08:23:21.889+08:00  INFO 21596 --- [http-nio-8889-exec-4] c.heating.service.impl.BillServiceImpl   : 找到缴费记录数量: 1
2025-08-19T08:23:21.889+08:00  INFO 21596 --- [http-nio-8889-exec-4] c.heating.service.impl.BillServiceImpl   : 缴费记录: ID=12, 金额=1009.55, 方式=微信支付, 日期=2025-08-14 09:10
2025-08-19T08:23:21.889+08:00  INFO 21596 --- [http-nio-8889-exec-4] c.heating.service.impl.BillServiceImpl   : 缴费记录构建完成，共 1 条记录
2025-08-19T08:23:21.889+08:00  INFO 21596 --- [http-nio-8889-exec-4] c.heating.service.impl.BillServiceImpl   : 简化账单信息获取成功
2025-08-19T08:23:21.889+08:00  INFO 21596 --- [http-nio-8889-exec-4] c.heating.controller.WeixinController    : 简化账单信息获取完成: code=200, message=获取账单信息成功
2025-08-19T08:23:21.890+08:00 DEBUG 21596 --- [http-nio-8889-exec-4] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-19T08:23:21.893+08:00  INFO 21596 --- [http-nio-8889-exec-7] c.heating.service.impl.BillServiceImpl   : 计费规则: 最低缴费比例=0.30
2025-08-19T08:23:21.893+08:00  INFO 21596 --- [http-nio-8889-exec-7] c.heating.service.impl.BillServiceImpl   : 管网维护费计算完成: 1009.5510 元 (账单金额 3365.17 * 最低比例 0.30)
2025-08-19T08:23:21.893+08:00  INFO 21596 --- [http-nio-8889-exec-7] c.heating.service.impl.BillServiceImpl   : 不用热状态 - 管网维护费: 1009.5510 元
2025-08-19T08:23:21.893+08:00  INFO 21596 --- [http-nio-8889-exec-7] c.heating.service.impl.BillServiceImpl   : 欠费金额: 0.00 元
2025-08-19T08:23:21.893+08:00  INFO 21596 --- [http-nio-8889-exec-7] c.heating.service.impl.BillServiceImpl   : 应缴费金额: 1009.5510 元
2025-08-19T08:23:21.893+08:00  INFO 21596 --- [http-nio-8889-exec-7] c.heating.service.impl.BillServiceImpl   : 实际缴费金额: 1009.55 元
2025-08-19T08:23:21.893+08:00  INFO 21596 --- [http-nio-8889-exec-7] c.heating.service.impl.BillServiceImpl   : 构建缴费状态信息，账单状态: partial_paid
2025-08-19T08:23:21.893+08:00  INFO 21596 --- [http-nio-8889-exec-7] c.heating.service.impl.BillServiceImpl   : 缴费状态信息构建完成: 状态=部分缴费, 显示实际缴费=true, 剩余金额=2355.62
2025-08-19T08:23:21.893+08:00  INFO 21596 --- [http-nio-8889-exec-7] c.heating.service.impl.BillServiceImpl   : 构建缴费记录列表，账单ID: 7
2025-08-19T08:23:21.929+08:00  INFO 21596 --- [http-nio-8889-exec-7] c.heating.service.impl.BillServiceImpl   : 找到缴费记录数量: 1
2025-08-19T08:23:21.929+08:00  INFO 21596 --- [http-nio-8889-exec-7] c.heating.service.impl.BillServiceImpl   : 缴费记录: ID=12, 金额=1009.55, 方式=微信支付, 日期=2025-08-14 09:10
2025-08-19T08:23:21.929+08:00  INFO 21596 --- [http-nio-8889-exec-7] c.heating.service.impl.BillServiceImpl   : 缴费记录构建完成，共 1 条记录
2025-08-19T08:23:21.929+08:00  INFO 21596 --- [http-nio-8889-exec-7] c.heating.service.impl.BillServiceImpl   : 简化账单信息获取成功
2025-08-19T08:23:21.929+08:00  INFO 21596 --- [http-nio-8889-exec-7] c.heating.controller.WeixinController    : 简化账单信息获取完成: code=200, message=获取账单信息成功
2025-08-19T08:23:21.930+08:00 DEBUG 21596 --- [http-nio-8889-exec-7] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-19T08:32:27.762+08:00 DEBUG 21596 --- [http-nio-8889-exec-6] o.s.security.web.FilterChainProxy        : Securing POST /api/weixin/bill/simple-info
2025-08-19T08:32:27.763+08:00 DEBUG 21596 --- [http-nio-8889-exec-6] o.s.security.web.FilterChainProxy        : Secured POST /api/weixin/bill/simple-info
2025-08-19T08:32:27.764+08:00  INFO 21596 --- [http-nio-8889-exec-6] c.heating.controller.WeixinController    : 收到获取简化账单信息请求: SimpleBillInfoRequest(houseId=7, heatingYear=2025)
2025-08-19T08:32:27.765+08:00 DEBUG 21596 --- [http-nio-8889-exec-3] o.s.security.web.FilterChainProxy        : Securing POST /api/weixin/bill/simple-info
2025-08-19T08:32:27.765+08:00  INFO 21596 --- [http-nio-8889-exec-6] c.heating.service.impl.BillServiceImpl   : 开始获取简化账单信息: SimpleBillInfoRequest(houseId=7, heatingYear=2025)
2025-08-19T08:32:27.765+08:00 DEBUG 21596 --- [http-nio-8889-exec-3] o.s.security.web.FilterChainProxy        : Secured POST /api/weixin/bill/simple-info
2025-08-19T08:32:27.766+08:00  INFO 21596 --- [http-nio-8889-exec-3] c.heating.controller.WeixinController    : 收到获取简化账单信息请求: SimpleBillInfoRequest(houseId=7, heatingYear=2025)
2025-08-19T08:32:27.766+08:00  INFO 21596 --- [http-nio-8889-exec-3] c.heating.service.impl.BillServiceImpl   : 开始获取简化账单信息: SimpleBillInfoRequest(houseId=7, heatingYear=2025)
2025-08-19T08:32:27.992+08:00  INFO 21596 --- [http-nio-8889-exec-6] c.heating.service.impl.BillServiceImpl   : 房屋信息: ID=7, 户号=HT2024000121, 用热状态=0
2025-08-19T08:32:27.992+08:00  INFO 21596 --- [http-nio-8889-exec-6] c.heating.service.impl.BillServiceImpl   : 查询供暖年度: 2025
2025-08-19T08:32:28.001+08:00  INFO 21596 --- [http-nio-8889-exec-3] c.heating.service.impl.BillServiceImpl   : 房屋信息: ID=7, 户号=HT2024000121, 用热状态=0
2025-08-19T08:32:28.002+08:00  INFO 21596 --- [http-nio-8889-exec-3] c.heating.service.impl.BillServiceImpl   : 查询供暖年度: 2025
2025-08-19T08:32:28.026+08:00  INFO 21596 --- [http-nio-8889-exec-6] c.heating.service.impl.BillServiceImpl   : 构建房屋信息
2025-08-19T08:32:28.026+08:00  INFO 21596 --- [http-nio-8889-exec-6] c.h.service.impl.HeatUnitServiceImpl     : 根据住户id获取小区名字: houseId=7
2025-08-19T08:32:28.037+08:00  INFO 21596 --- [http-nio-8889-exec-3] c.heating.service.impl.BillServiceImpl   : 构建房屋信息
2025-08-19T08:32:28.037+08:00  INFO 21596 --- [http-nio-8889-exec-3] c.h.service.impl.HeatUnitServiceImpl     : 根据住户id获取小区名字: houseId=7
2025-08-19T08:32:28.412+08:00  INFO 21596 --- [http-nio-8889-exec-6] c.h.service.impl.HeatUnitServiceImpl     : 成功获取小区名字: houseId=7, heatUnitId=1, communityName=印象小区
2025-08-19T08:32:28.412+08:00  INFO 21596 --- [http-nio-8889-exec-6] c.heating.service.impl.BillServiceImpl   : 房屋信息构建完成: 用热状态=不供暖
2025-08-19T08:32:28.412+08:00  INFO 21596 --- [http-nio-8889-exec-6] c.heating.service.impl.BillServiceImpl   : 账单信息: ID=7, 总金额=3365.17, 已缴金额=1009.55, 欠费金额=0.00, 状态=partial_paid
2025-08-19T08:32:28.412+08:00  INFO 21596 --- [http-nio-8889-exec-6] c.heating.service.impl.BillServiceImpl   : 构建账单费用信息，用热状态: 0
2025-08-19T08:32:28.440+08:00  INFO 21596 --- [http-nio-8889-exec-3] c.h.service.impl.HeatUnitServiceImpl     : 成功获取小区名字: houseId=7, heatUnitId=1, communityName=印象小区
2025-08-19T08:32:28.440+08:00  INFO 21596 --- [http-nio-8889-exec-3] c.heating.service.impl.BillServiceImpl   : 房屋信息构建完成: 用热状态=不供暖
2025-08-19T08:32:28.440+08:00  INFO 21596 --- [http-nio-8889-exec-3] c.heating.service.impl.BillServiceImpl   : 账单信息: ID=7, 总金额=3365.17, 已缴金额=1009.55, 欠费金额=0.00, 状态=partial_paid
2025-08-19T08:32:28.440+08:00  INFO 21596 --- [http-nio-8889-exec-3] c.heating.service.impl.BillServiceImpl   : 构建账单费用信息，用热状态: 0
2025-08-19T08:32:28.446+08:00  INFO 21596 --- [http-nio-8889-exec-6] c.h.s.impl.HeatingFeeRuleServiceImpl     : 获取到规则ID=1的单价: 5.80
2025-08-19T08:32:28.446+08:00  INFO 21596 --- [http-nio-8889-exec-6] c.heating.service.impl.BillServiceImpl   : 单价: 5.80 元/㎡
2025-08-19T08:32:28.446+08:00  INFO 21596 --- [http-nio-8889-exec-6] c.heating.service.impl.BillServiceImpl   : 计算管网维护费，房屋面积: 116.04, 计费规则ID: 1
2025-08-19T08:32:28.476+08:00  INFO 21596 --- [http-nio-8889-exec-3] c.h.s.impl.HeatingFeeRuleServiceImpl     : 获取到规则ID=1的单价: 5.80
2025-08-19T08:32:28.476+08:00  INFO 21596 --- [http-nio-8889-exec-3] c.heating.service.impl.BillServiceImpl   : 单价: 5.80 元/㎡
2025-08-19T08:32:28.476+08:00  INFO 21596 --- [http-nio-8889-exec-3] c.heating.service.impl.BillServiceImpl   : 计算管网维护费，房屋面积: 116.04, 计费规则ID: 1
2025-08-19T08:32:28.639+08:00  INFO 21596 --- [http-nio-8889-exec-6] c.heating.service.impl.BillServiceImpl   : 计费规则: 最低缴费比例=0.30
2025-08-19T08:32:28.639+08:00  INFO 21596 --- [http-nio-8889-exec-6] c.heating.service.impl.BillServiceImpl   : 管网维护费计算完成: 1009.5510 元 (账单金额 3365.17 * 最低比例 0.30)
2025-08-19T08:32:28.639+08:00  INFO 21596 --- [http-nio-8889-exec-6] c.heating.service.impl.BillServiceImpl   : 不用热状态 - 管网维护费: 1009.5510 元
2025-08-19T08:32:28.639+08:00  INFO 21596 --- [http-nio-8889-exec-6] c.heating.service.impl.BillServiceImpl   : 欠费金额: 0.00 元
2025-08-19T08:32:28.639+08:00  INFO 21596 --- [http-nio-8889-exec-6] c.heating.service.impl.BillServiceImpl   : 应缴费金额: 1009.5510 元
2025-08-19T08:32:28.639+08:00  INFO 21596 --- [http-nio-8889-exec-6] c.heating.service.impl.BillServiceImpl   : 实际缴费金额: 1009.55 元
2025-08-19T08:32:28.639+08:00  INFO 21596 --- [http-nio-8889-exec-6] c.heating.service.impl.BillServiceImpl   : 构建缴费状态信息，账单状态: partial_paid
2025-08-19T08:32:28.639+08:00  INFO 21596 --- [http-nio-8889-exec-6] c.heating.service.impl.BillServiceImpl   : 缴费状态信息构建完成: 状态=部分缴费, 显示实际缴费=true, 剩余金额=2355.62
2025-08-19T08:32:28.639+08:00  INFO 21596 --- [http-nio-8889-exec-6] c.heating.service.impl.BillServiceImpl   : 构建缴费记录列表，账单ID: 7
2025-08-19T08:32:28.673+08:00  INFO 21596 --- [http-nio-8889-exec-6] c.heating.service.impl.BillServiceImpl   : 找到缴费记录数量: 1
2025-08-19T08:32:28.673+08:00  INFO 21596 --- [http-nio-8889-exec-6] c.heating.service.impl.BillServiceImpl   : 缴费记录: ID=12, 金额=1009.55, 方式=微信支付, 日期=2025-08-14 09:10
2025-08-19T08:32:28.673+08:00  INFO 21596 --- [http-nio-8889-exec-6] c.heating.service.impl.BillServiceImpl   : 缴费记录构建完成，共 1 条记录
2025-08-19T08:32:28.673+08:00  INFO 21596 --- [http-nio-8889-exec-6] c.heating.service.impl.BillServiceImpl   : 简化账单信息获取成功
2025-08-19T08:32:28.674+08:00  INFO 21596 --- [http-nio-8889-exec-6] c.heating.controller.WeixinController    : 简化账单信息获取完成: code=200, message=获取账单信息成功
2025-08-19T08:32:28.674+08:00 DEBUG 21596 --- [http-nio-8889-exec-6] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
2025-08-19T08:32:28.677+08:00  INFO 21596 --- [http-nio-8889-exec-3] c.heating.service.impl.BillServiceImpl   : 计费规则: 最低缴费比例=0.30
2025-08-19T08:32:28.677+08:00  INFO 21596 --- [http-nio-8889-exec-3] c.heating.service.impl.BillServiceImpl   : 管网维护费计算完成: 1009.5510 元 (账单金额 3365.17 * 最低比例 0.30)
2025-08-19T08:32:28.677+08:00  INFO 21596 --- [http-nio-8889-exec-3] c.heating.service.impl.BillServiceImpl   : 不用热状态 - 管网维护费: 1009.5510 元
2025-08-19T08:32:28.677+08:00  INFO 21596 --- [http-nio-8889-exec-3] c.heating.service.impl.BillServiceImpl   : 欠费金额: 0.00 元
2025-08-19T08:32:28.677+08:00  INFO 21596 --- [http-nio-8889-exec-3] c.heating.service.impl.BillServiceImpl   : 应缴费金额: 1009.5510 元
2025-08-19T08:32:28.678+08:00  INFO 21596 --- [http-nio-8889-exec-3] c.heating.service.impl.BillServiceImpl   : 实际缴费金额: 1009.55 元
2025-08-19T08:32:28.678+08:00  INFO 21596 --- [http-nio-8889-exec-3] c.heating.service.impl.BillServiceImpl   : 构建缴费状态信息，账单状态: partial_paid
2025-08-19T08:32:28.678+08:00  INFO 21596 --- [http-nio-8889-exec-3] c.heating.service.impl.BillServiceImpl   : 缴费状态信息构建完成: 状态=部分缴费, 显示实际缴费=true, 剩余金额=2355.62
2025-08-19T08:32:28.678+08:00  INFO 21596 --- [http-nio-8889-exec-3] c.heating.service.impl.BillServiceImpl   : 构建缴费记录列表，账单ID: 7
2025-08-19T08:32:28.714+08:00  INFO 21596 --- [http-nio-8889-exec-3] c.heating.service.impl.BillServiceImpl   : 找到缴费记录数量: 1
2025-08-19T08:32:28.714+08:00  INFO 21596 --- [http-nio-8889-exec-3] c.heating.service.impl.BillServiceImpl   : 缴费记录: ID=12, 金额=1009.55, 方式=微信支付, 日期=2025-08-14 09:10
2025-08-19T08:32:28.714+08:00  INFO 21596 --- [http-nio-8889-exec-3] c.heating.service.impl.BillServiceImpl   : 缴费记录构建完成，共 1 条记录
2025-08-19T08:32:28.714+08:00  INFO 21596 --- [http-nio-8889-exec-3] c.heating.service.impl.BillServiceImpl   : 简化账单信息获取成功
2025-08-19T08:32:28.714+08:00  INFO 21596 --- [http-nio-8889-exec-3] c.heating.controller.WeixinController    : 简化账单信息获取完成: code=200, message=获取账单信息成功
2025-08-19T08:32:28.714+08:00 DEBUG 21596 --- [http-nio-8889-exec-3] o.s.s.w.a.AnonymousAuthenticationFilter  : Set SecurityContextHolder to anonymous SecurityContext
