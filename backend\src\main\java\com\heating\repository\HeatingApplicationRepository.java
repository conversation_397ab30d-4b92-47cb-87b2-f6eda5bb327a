package com.heating.repository;

import com.heating.entity.bill.THeatingApplication;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 用热申请数据访问接口
 */
@Repository
public interface HeatingApplicationRepository extends JpaRepository<THeatingApplication, Long> {
    
    /**
     * 根据房屋ID查询申请列表，按创建时间倒序
     * @param houseId 房屋ID
     * @return 申请列表
     */
    List<THeatingApplication> findByHouseIdOrderByCreatedAtDesc(Long houseId);
    
    /**
     * 查询所有申请列表，按创建时间倒序
     * @return 申请列表
     */
    List<THeatingApplication> findAllByOrderByCreatedAtDesc();
    
    /**
     * 根据房屋ID和申请季度查询申请记录
     * @param houseId 房屋ID
     * @param applySeason 申请季度
     * @return 申请列表
     */
    List<THeatingApplication> findByHouseIdAndApplySeason(Long houseId, String applySeason);
    
    /**
     * 根据申请状态查询申请列表
     * @param status 申请状态
     * @return 申请列表
     */
    List<THeatingApplication> findByCurrentStatusOrderByCreatedAtDesc(THeatingApplication.ApplicationStatus status);
    
    /**
     * 根据房屋ID和状态查询申请记录
     * @param houseId 房屋ID
     * @param status 申请状态
     * @return 申请列表
     */
    List<THeatingApplication> findByHouseIdAndCurrentStatus(Long houseId, THeatingApplication.ApplicationStatus status);
    
    /**
     * 统计某个房屋在指定季度的待审核申请数量
     * @param houseId 房屋ID
     * @param applySeason 申请季度
     * @return 申请数量
     */
    @Query("SELECT COUNT(h) FROM THeatingApplication h WHERE h.houseId = :houseId AND h.applySeason = :applySeason AND h.currentStatus = 'pending'")
    long countPendingApplicationsByHouseAndSeason(@Param("houseId") Long houseId, @Param("applySeason") String applySeason);
}
