package com.heating.controller;

import com.heating.dto.ApiResponse;
import com.heating.dto.bill.*;
import com.heating.dto.fault.FaultReportRequest;
import com.heating.dto.payment.PaymentSubmitRequest;
import com.heating.dto.payment.PaymentSubmitResponse;
import com.heating.dto.user.*;
import com.heating.entity.bill.TStopSupplyApply;
import com.heating.entity.bill.THeatingApplication;
import com.heating.entity.system.DictData;
import com.heating.entity.user.TUserWeixin;
import com.heating.exception.GlobalExceptionHandler;
import com.heating.service.*;
import com.heating.vo.user.WeixinLoginResponse;
import com.heating.vo.user.WeixinUserInfoResponse;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@RestController
@RequestMapping("/api/weixin")
@CrossOrigin(origins = "*")
public class WeixinController {

    @Autowired
    private TUserWeixinService userWeixinService;

    @Autowired
    private BillService billService;

    @Autowired
    private StopSupplyService stopSupplyService;

    @Autowired
    private HeatingApplicationService heatingApplicationService;

    @Autowired
    private DictDataService dictDataService;

    @Autowired
    private PaymentService paymentService;

    private static final Logger logger = LoggerFactory.getLogger(FaultController.class);

    @Autowired
    private FaultService faultService; // Service for handling fault-related operations

    @Autowired
    private WorkOrderService workOrderService; // Service for handling work order operations
    // 如果使用token方式，需要注入JwtUtil
    // @Autowired
    // private JwtUtil jwtUtil;

    /**
     * 用户登录 - 使用手机号登录
     */
    @PostMapping("/login")
    public ResponseEntity<Map<String, Object>> login(@Validated @RequestBody WeixinLoginRequest request) {
        try {
            WeixinLoginResponse response = userWeixinService.login(request);
            if (response.isSuccess()) {
                return ResponseEntity.ok(Map.of(
                        "code", 200,
                        "message", response.getMessage(),
                        "token", response.getToken(),
                        "userInfo", response.getUserInfo()
                ));
            } else {
                return ResponseEntity.ok(Map.of(
                        "code", 400,
                        "message", response.getMessage()
                ));
            }
        } catch (Exception e) {
            log.error("登录失败", e);
            return ResponseEntity.ok(Map.of(
                    "code", 400,
                    "message", "登录失败：" + e.getMessage()
            ));
        }
    }

    /**
     * 用户注册 - 使用手机号注册
     */
    @PostMapping("/register")
    public ResponseEntity<Map<String, Object>> register(@RequestBody WeixinRegisterRequest request) {
        try {
            log.info("收到注册请求: {}", request);
            
            boolean success = userWeixinService.register(request);
            if (success) {
                return ResponseEntity.ok(Map.of(
                        "code", 200,
                        "message", "注册成功"
                ));
            } else {
                return ResponseEntity.ok(Map.of(
                        "code", 400,
                        "message", "注册失败"
                ));
            }
        } catch (Exception e) {
            log.error("注册失败", e);
            return ResponseEntity.ok(Map.of(
                    "code", 400,
                    "message", "注册失败：" + e.getMessage()
            ));
        }
    }

    /**
     * 绑定户号
     */
    @PostMapping("/bind-house")
    public ResponseEntity<Map<String, Object>> bindHouse(@Validated @RequestBody BindHouseRequest request) {
        try {
            boolean success = userWeixinService.bindHouseByUsername(request.getUsername(), request.getHouseNumber());
            if (success) {
                // 获取用户信息
                TUserWeixin user = userWeixinService.getByUsername(request.getUsername());
                WeixinUserInfoResponse userInfo = userWeixinService.getUserInfo(user.getId());
                return ResponseEntity.ok(Map.of(
                        "code", 200,
                        "message", "绑定成功",
                        "data", userInfo
                ));
            } else {
                return ResponseEntity.ok(Map.of(
                        "code", 400,
                        "message", "绑定失败"
                ));
            }
        } catch (Exception e) {
            log.error("绑定户号失败", e);
            return ResponseEntity.ok(Map.of(
                    "code", 400,
                    "message", "绑定失败：" + e.getMessage()
            ));
        }
    }

    /**
     * 获取用户信息
     */
    @GetMapping("/user-info/{userId}")
    public ResponseEntity<Map<String, Object>> getUserInfo(@PathVariable Long userId) {
        try {
            WeixinUserInfoResponse userInfo = userWeixinService.getUserInfo(userId);
            if (userInfo != null) {
                return ResponseEntity.ok(Map.of(
                        "code", 200,
                        "message", "获取用户信息成功",
                        "data", userInfo
                ));
            } else {
                return ResponseEntity.ok(Map.of(
                        "code", 400,
                        "message", "用户不存在"
                ));
            }
        } catch (Exception e) {
            log.error("获取用户信息失败", e);
            return ResponseEntity.ok(Map.of(
                    "code", 400,
                    "message", "获取用户信息失败：" + e.getMessage()
            ));
        }
    }

    /**
     * 微信授权登录
     */
    @PostMapping("/wx-login")
    public ResponseEntity<Map<String, Object>> weixinLogin(@Validated @RequestBody WeixinAuthLoginRequest request) {
        try {
            WeixinLoginResponse response = userWeixinService.weixinLogin(request);
            if (response.isSuccess()) {
                return ResponseEntity.ok(Map.of(
                        "code", 200,
                        "message", response.getMessage(),
                        "token", response.getToken(),
                        "userInfo", response.getUserInfo(),
                        "isNewUser", response.isNewUser()
                ));
            } else {
                return ResponseEntity.ok(Map.of(
                        "code", 400,
                        "message", response.getMessage()
                ));
            }
        } catch (Exception e) {
            log.error("微信登录失败", e);
            return ResponseEntity.ok(Map.of(
                    "code", 400,
                    "message", "微信登录失败：" + e.getMessage()
            ));
        }
    }

    /**
     * 微信手机号注册
     */
    @PostMapping("/wx-register")
    public ResponseEntity<Map<String, Object>> weixinRegister(@Validated @RequestBody WeixinPhoneRegisterRequest request) {
        try {
            boolean success = userWeixinService.weixinRegister(request);
            if (success) {
                return ResponseEntity.ok(Map.of(
                        "code", 200,
                        "message", "注册成功"
                ));
            } else {
                return ResponseEntity.ok(Map.of(
                        "code", 400,
                        "message", "注册失败"
                ));
            }
        } catch (Exception e) {
            log.error("微信注册失败", e);
            return ResponseEntity.ok(Map.of(
                    "code", 400,
                    "message", "注册失败：" + e.getMessage()
            ));
        }
    }

    /**
     * 获取账单列表
     */
    @PostMapping("/bills")
    public ResponseEntity<ApiResponse<BillListResponse>> getBillList(@RequestBody BillListRequest request) {
        try {
            log.info("获取账单列表请求: {}", request);
            
            // 从请求参数获取用户ID
            Long userId = request.getUserId();
            if (userId == null) {
                return ResponseEntity.badRequest().body(ApiResponse.error("用户ID不能为空"));
            }
            
            TUserWeixin user = userWeixinService.getById(userId);
            if (user == null || user.getHouseId() == null) {
                return ResponseEntity.badRequest().body(ApiResponse.error("用户未绑定房屋信息"));
            }
            
            // 设置房屋ID
            request.setHouseId(user.getHouseId());
            
            BillListResponse response = billService.getBillList(request);
            return ResponseEntity.ok(ApiResponse.success("获取账单列表成功", response));
            
        } catch (Exception e) {
            log.error("获取账单列表失败", e);
            return ResponseEntity.status(500).body(ApiResponse.error("获取账单列表失败: " + e.getMessage()));
        }
    }

    /**
     * 获取账单详情
     */
    @PostMapping("/bill-detail")
    public ResponseEntity<ApiResponse<BillListResponse.BillDetail>> getBillDetail(@RequestBody BillDetailRequest request) {
        try {
            log.info("获取账单详情请求: {}", request);
            
            // 从请求参数获取用户ID
            Long userId = request.getUserId();
            if (userId == null) {
                return ResponseEntity.badRequest().body(ApiResponse.error("用户ID不能为空"));
            }
            
            // 验证用户权限
            TUserWeixin user = userWeixinService.getById(userId);
            if (user == null) {
                return ResponseEntity.badRequest().body(ApiResponse.error("用户不存在"));
            }
            
            BillListResponse.BillDetail response = billService.getBillDetail(request);
            return ResponseEntity.ok(ApiResponse.success("获取账单详情成功", response));
            
        } catch (Exception e) {
            log.error("获取账单详情失败", e);
            return ResponseEntity.badRequest().body(ApiResponse.error("获取账单详情失败: " + e.getMessage()));
        }
    }

    /**
     * 获取缴费记录列表
     */
    @PostMapping("/payments")
    public ResponseEntity<ApiResponse<PaymentListResponse>> getPaymentList(@RequestBody BillDetailRequest request) {
        try {
            log.info("收到获取缴费记录请求: {}", request);
            
            Long billId = request.getBillId();
            if (billId == null) {
                log.warn("账单ID为空");
                return ResponseEntity.badRequest().body(ApiResponse.error("账单ID不能为空"));
            }
            
            log.info("开始查询账单ID为{}的缴费记录", billId);
            PaymentListResponse response = billService.getPaymentListByBillId(billId);
            log.info("查询到{}条缴费记录", response.getTotal());
            
            return ResponseEntity.ok(ApiResponse.success("获取缴费记录列表成功", response));
            
        } catch (Exception e) {
            log.error("获取缴费记录列表失败，请求参数: {}", request, e);
            return ResponseEntity.status(500).body(ApiResponse.error("获取缴费记录列表失败: " + e.getMessage()));
        }
    }

    /**
     * 根据房屋ID获取所有缴费记录
     */
    @PostMapping("/payment-records")
    public ResponseEntity<ApiResponse<PaymentListResponse>> getPaymentRecordsByHouse(@RequestBody BillDetailRequest request) {
        try {
            // 从请求参数获取用户ID
            Long houseId = request.getHouseId();
            log.info("根据房屋ID获取缴费记录: houseId={}", houseId);
            
            PaymentListResponse response = billService.getPaymentRecordsByHouseId(houseId);
            return ResponseEntity.ok(ApiResponse.success("获取缴费记录成功", response));
            
        } catch (Exception e) {
            log.error("获取缴费记录失败", e);
            return ResponseEntity.status(500).body(ApiResponse.error("获取缴费记录失败: " + e.getMessage()));
        }
    }

    /**
     * 获取票据详情
     */
    @PostMapping("/invoice-detail")
    public ResponseEntity<ApiResponse<InvoiceDetailResponse>> getInvoiceDetail(@RequestBody Map<String, Object> request) {
        try {
            Object paymentIdObj = request.get("paymentId");
            if (paymentIdObj == null) {
                return ResponseEntity.badRequest().body(ApiResponse.error("缴费记录ID不能为空"));
            }
            
            Long paymentId = Long.valueOf(paymentIdObj.toString());
            log.info("获取票据详情: paymentId={}", paymentId);
            
            InvoiceDetailResponse response = billService.getInvoiceDetail(paymentId);
            return ResponseEntity.ok(ApiResponse.success("获取票据详情成功", response));
            
        } catch (Exception e) {
            log.error("获取票据详情失败", e);
            return ResponseEntity.status(500).body(ApiResponse.error("获取票据详情失败: " + e.getMessage()));
        }
    }

    /**
     * 下载票据PDF
     */
    @PostMapping("/download-invoice-pdf")
    public ResponseEntity<ApiResponse<String>> downloadInvoicePDF(@RequestBody Map<String, Object> request) {
        try {
            Object paymentIdObj = request.get("paymentId");
            if (paymentIdObj == null) {
                return ResponseEntity.badRequest().body(ApiResponse.error("缴费记录ID不能为空"));
            }
            
            Long paymentId = Long.valueOf(paymentIdObj.toString());
            log.info("下载票据PDF: paymentId={}", paymentId);
            
            String pdfUrl = billService.generateInvoicePDF(paymentId);
            return ResponseEntity.ok(ApiResponse.success("PDF生成成功", pdfUrl));
            
        } catch (Exception e) {
            log.error("生成PDF失败", e);
            return ResponseEntity.status(500).body(ApiResponse.error("生成PDF失败: " + e.getMessage()));
        }
    }

    /**
     * 提交停供申请
     */
    @PostMapping("/stop-supply/apply")
    public ResponseEntity<Map<String, Object>> submitStopSupplyApply(@RequestBody StopSupplyApplyRequest request) {
        try {
            log.info("收到停供申请: {}", request);
            
            Long applyId = stopSupplyService.submitApply(request);
            return ResponseEntity.ok(Map.of(
                    "code", 200,
                    "message", "停供申请提交成功",
                    "data", Map.of("applyId", applyId)
            ));
        } catch (Exception e) {
            log.error("停供申请提交失败", e);
            return ResponseEntity.ok(Map.of(
                    "code", 400,
                    "message", "申请提交失败：" + e.getMessage()
            ));
        }
    }

    /**
     * 获取停供申请列表
     */
    @GetMapping("/stop-supply/list")
    public ResponseEntity<Map<String, Object>> getStopSupplyList(@RequestParam(required = false) Long houseId) {
        try {
            List<TStopSupplyApply> applyList = stopSupplyService.getApplyList(houseId);
            return ResponseEntity.ok(Map.of(
                    "code", 200,
                    "message", "获取申请列表成功",
                    "data", applyList
            ));
        } catch (Exception e) {
            log.error("获取停供申请列表失败", e);
            return ResponseEntity.ok(Map.of(
                    "code", 400,
                    "message", "获取申请列表失败：" + e.getMessage()
            ));
        }
    }

    /**
     * 获取停供申请详情
     */
    @GetMapping("/stop-supply/detail")
    public ResponseEntity<Map<String, Object>> getStopSupplyDetail(@RequestParam Long id) {
        try {
            TStopSupplyApply apply = stopSupplyService.getApplyDetail(id);
            if (apply == null) {
                return ResponseEntity.ok(Map.of(
                        "code", 404,
                        "message", "申请记录不存在"
                ));
            }
            return ResponseEntity.ok(Map.of(
                    "code", 200,
                    "message", "获取申请详情成功",
                    "data", apply
            ));
        } catch (Exception e) {
            log.error("获取停供申请详情失败", e);
            return ResponseEntity.ok(Map.of(
                    "code", 400,
                    "message", "获取申请详情失败：" + e.getMessage()
            ));
        }
    }

    /**
     * 取消停供申请
     */
    @PostMapping("/stop-supply/cancel")
    public ResponseEntity<Map<String, Object>> cancelStopSupplyApply(@RequestBody Map<String, Object> request) {
        try {
            Object idObj = request.get("id");
            if (idObj == null) {
                return ResponseEntity.badRequest().body(Map.of(
                        "code", 400,
                        "message", "申请ID不能为空"
                ));
            }

            Long applyId = Long.valueOf(idObj.toString());
            stopSupplyService.cancelApply(applyId);

            return ResponseEntity.ok(Map.of(
                    "code", 200,
                    "message", "取消申请成功"
            ));
        } catch (Exception e) {
            log.error("取消停供申请失败", e);
            return ResponseEntity.ok(Map.of(
                    "code", 400,
                    "message", "取消申请失败：" + e.getMessage()
            ));
        }
    }

    // ==================== 用热申请相关接口 ====================

    /**
     * 提交用热申请
     */
    @PostMapping("/heating-application/apply")
    public ResponseEntity<Map<String, Object>> submitHeatingApplication(@RequestBody HeatingApplicationRequest request) {
        try {
            log.info("收到用热申请: {}", request);

            Long applicationId = heatingApplicationService.submitApplication(request);
            return ResponseEntity.ok(Map.of(
                    "code", 200,
                    "message", "用热申请提交成功",
                    "data", Map.of("applicationId", applicationId)
            ));
        } catch (Exception e) {
            log.error("用热申请提交失败", e);
            return ResponseEntity.ok(Map.of(
                    "code", 400,
                    "message", "申请提交失败：" + e.getMessage()
            ));
        }
    }

    /**
     * 获取用热申请列表
     */
    @GetMapping("/heating-application/list")
    public ResponseEntity<Map<String, Object>> getHeatingApplicationList(@RequestParam(required = false) Long houseId) {
        try {
            List<THeatingApplication> applicationList = heatingApplicationService.getApplicationList(houseId);
            return ResponseEntity.ok(Map.of(
                    "code", 200,
                    "message", "获取申请列表成功",
                    "data", applicationList
            ));
        } catch (Exception e) {
            log.error("获取用热申请列表失败", e);
            return ResponseEntity.ok(Map.of(
                    "code", 400,
                    "message", "获取申请列表失败：" + e.getMessage()
            ));
        }
    }

    /**
     * 获取用热申请详情
     */
    @GetMapping("/heating-application/detail")
    public ResponseEntity<Map<String, Object>> getHeatingApplicationDetail(@RequestParam Long id) {
        try {
            THeatingApplication application = heatingApplicationService.getApplicationDetail(id);
            if (application == null) {
                return ResponseEntity.ok(Map.of(
                        "code", 404,
                        "message", "申请记录不存在"
                ));
            }
            return ResponseEntity.ok(Map.of(
                    "code", 200,
                    "message", "获取申请详情成功",
                    "data", application
            ));
        } catch (Exception e) {
            log.error("获取用热申请详情失败", e);
            return ResponseEntity.ok(Map.of(
                    "code", 400,
                    "message", "获取申请详情失败：" + e.getMessage()
            ));
        }
    }

    /**
     * 取消用热申请
     */
    @PostMapping("/heating-application/cancel")
    public ResponseEntity<Map<String, Object>> cancelHeatingApplication(@RequestBody Map<String, Object> request) {
        try {
            Object idObj = request.get("id");
            if (idObj == null) {
                return ResponseEntity.badRequest().body(Map.of(
                        "code", 400,
                        "message", "申请ID不能为空"
                ));
            }

            Long applicationId = Long.valueOf(idObj.toString());
            heatingApplicationService.cancelApplication(applicationId);

            return ResponseEntity.ok(Map.of(
                    "code", 200,
                    "message", "取消申请成功"
            ));
        } catch (Exception e) {
            log.error("取消用热申请失败", e);
            return ResponseEntity.ok(Map.of(
                    "code", 400,
                    "message", "取消申请失败：" + e.getMessage()
            ));
        }
    }

    /**
     * 在线缴费接口
     *  1. 处理微信小程序的在线支付成功后的缴费记录生成和账单更新
     *  2. 根据用户是否用热更新t_house住户表的is_heating状态
     *  3. 查看是否有欠费记录，同时清除掉欠费记录状态为cleared已清除状态
     */
    @PostMapping("/payment/online-pay")
    public ResponseEntity<Map<String, Object>> processOnlinePayment(@RequestBody OnlinePaymentRequest request) {
        try {
            log.info("收到在线缴费请求: {}", request);
            log.info("请求详情 - billId: {}, houseId: {}, amount: {}, paymentMethod: {}, transactionNo: {}",
                    request.getBillId(), request.getHouseId(), request.getAmount(),
                    request.getPaymentMethod(), request.getTransactionNo());

            // 处理在线缴费
            OnlinePaymentResponse response = paymentService.processOnlinePayment(request);

            log.info("在线缴费处理成功: paymentId={}, billId={}", response.getPaymentId(), response.getBillId());

            return ResponseEntity.ok(Map.of(
                    "code", 200,
                    "message", "缴费成功",
                    "data", response
            ));

        } catch (Exception e) {
            log.error("在线缴费处理失败: {}", e.getMessage(), e);

            // 返回更详细的错误信息
            String errorMessage = e.getMessage();
            if (errorMessage == null || errorMessage.trim().isEmpty()) {
                errorMessage = "系统内部错误，请联系客服";
            }

            return ResponseEntity.ok(Map.of(
                    "code", 500,
                    "message", "缴费失败：" + errorMessage,
                    "error", e.getClass().getSimpleName()
            ));
        }
    }

    /**
     * 查看账单详情接口
     * 获取完整的账单信息，包括基本信息、账单信息、缴费记录、逾期记录
     */
    @PostMapping("/bill/view-detail")
    public ResponseEntity<Map<String, Object>> viewBillDetail(@RequestBody BillDetailViewRequest request) {
        try {
            log.info("收到查看账单详情请求: {}", request);

            // 从请求参数获取用户ID（实际应该从token中获取）
            Long userId = request.getUserId();
            if (userId == null) {
                return ResponseEntity.badRequest().body(Map.of(
                        "code", 400,
                        "message", "用户ID不能为空"
                ));
            }

            // 验证用户权限并获取房屋信息
            TUserWeixin user = userWeixinService.getById(userId);
            if (user == null || user.getHouseId() == null) {
                return ResponseEntity.badRequest().body(Map.of(
                        "code", 400,
                        "message", "用户未绑定房屋信息"
                ));
            }

            // 设置房屋ID（如果请求中没有指定，则使用用户绑定的房屋）
            if (request.getHouseId() == null) {
                request.setHouseId(user.getHouseId());
            }

            // 调用服务获取账单详情
            BillDetailViewResponse response = billService.viewBillDetail(request);

            return ResponseEntity.ok(Map.of(
                    "code", 200,
                    "message", "获取账单详情成功",
                    "data", response
            ));

        } catch (Exception e) {
            log.error("查看账单详情失败", e);
            return ResponseEntity.ok(Map.of(
                    "code", 400,
                    "message", "获取账单详情失败：" + e.getMessage()
            ));
        }
    }
    @GetMapping("/data/{dictId}")
    public ResponseEntity<?> getDictDataByDictId(@PathVariable Integer dictId) {
        List<DictData> dictDataList = dictDataService.getDictDataByDictId(dictId);

        Map<String, Object> response = new HashMap<>();
        response.put("code", 200);
        response.put("message", "获取字典数据成功");
        response.put("data", dictDataList);

        return ResponseEntity.ok(response);
    }

    /**
     * 上报故障接口
     * @param request 故障报告请求体
     * @return 返回故障报告的结果
     */
    @PostMapping("/report")
    public ResponseEntity<com.heating.util.ApiResponse<?>> reportFault(@RequestBody FaultReportRequest request) {
        logger.info("上报故障: {}", request); // Log the fault report request
        try {
            faultService.reportFault(request);
            return ResponseEntity.ok(
                    com.heating.util.ApiResponse.success("故障上报成功", null)    // Return success response
            );
        } catch (Exception e) {
            logger.error("上报故障时出错: {}", e.getMessage(), e); // Log error
            return GlobalExceptionHandler.errorResponseEntity(
                    "上报故障错误: " + e.getMessage(),
                    HttpStatus.INTERNAL_SERVER_ERROR // Return error response
            );
        }
    }

    /**
     * 获取用户故障历史记录列表
     * @param userId 用户ID
     * @param page 页码（从1开始）
     * @param size 每页大小
     * @param status 故障状态（可选）
     * @return 故障历史记录列表
     */
    @GetMapping("/fault-history")
    public ResponseEntity<Map<String, Object>> getFaultHistory(
            @RequestParam Long userId,
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(required = false) String status) {
        try {
            log.info("获取用户故障历史记录，用户ID: {}, 页码: {}, 每页大小: {}, 状态: {}", userId, page, size, status);

            // 验证用户是否存在
            TUserWeixin user = userWeixinService.getById(userId);
            if (user == null) {
                return ResponseEntity.ok(Map.of(
                        "code", 400,
                        "message", "用户不存在"
                ));
            }

            // 获取用户绑定的房屋ID
            Long houseId = user.getHouseId();
            if (houseId == null) {
                return ResponseEntity.ok(Map.of(
                        "code", 400,
                        "message", "用户未绑定房屋信息"
                ));
            }

            // 调用服务获取故障历史记录
            Map<String, Object> result = faultService.getFaultHistoryByHouseId(houseId, page, size, status);

            return ResponseEntity.ok(Map.of(
                    "code", 200,
                    "message", "获取故障历史记录成功",
                    "data", result
            ));

        } catch (Exception e) {
            log.error("获取故障历史记录失败", e);
            return ResponseEntity.ok(Map.of(
                    "code", 400,
                    "message", "获取故障历史记录失败：" + e.getMessage()
            ));
        }
    }

    /**
     * 获取故障详情及跟踪记录
     * @param faultId 故障ID
     * @param userId 用户ID（用于权限验证）
     * @return 故障详情和跟踪记录
     */
    @GetMapping("/fault-detail")
    public ResponseEntity<Map<String, Object>> getFaultDetail(
            @RequestParam Long faultId,
            @RequestParam Long userId) {
        try {
            log.info("获取故障详情，故障ID: {}, 用户ID: {}", faultId, userId);

            // 验证用户是否存在
            TUserWeixin user = userWeixinService.getById(userId);
            if (user == null) {
                return ResponseEntity.ok(Map.of(
                        "code", 400,
                        "message", "用户不存在"
                ));
            }

            // 获取故障详情
            Map<String, Object> faultDetail = faultService.getFaultDetailById(faultId);
            if (faultDetail == null) {
                return ResponseEntity.ok(Map.of(
                        "code", 404,
                        "message", "故障记录不存在"
                ));
            }

            // 验证故障是否属于该用户的房屋
            Long faultHouseId = (Long) faultDetail.get("house_id");
            if (faultHouseId == null || !faultHouseId.equals(user.getHouseId())) {
                return ResponseEntity.ok(Map.of(
                        "code", 403,
                        "message", "无权限查看该故障记录"
                ));
            }

            // 获取故障跟踪记录（工单信息）
            Map<String, Object> trackingInfo = workOrderService.getTrackingInfoByFaultId(faultId);

            // 组装返回数据
            Map<String, Object> result = new HashMap<>();
            result.put("faultDetail", faultDetail);
            result.put("trackingInfo", trackingInfo);

            return ResponseEntity.ok(Map.of(
                    "code", 200,
                    "message", "获取故障详情成功",
                    "data", result
            ));

        } catch (Exception e) {
            log.error("获取故障详情失败", e);
            return ResponseEntity.ok(Map.of(
                    "code", 400,
                    "message", "获取故障详情失败：" + e.getMessage()
            ));
        }
    }

    /**
     * 获取故障统计信息
     * @param userId 用户ID
     * @return 故障统计信息
     */
    @GetMapping("/fault-statistics")
    public ResponseEntity<Map<String, Object>> getFaultStatistics(@RequestParam Long userId) {
        try {
            log.info("获取用户故障统计信息，用户ID: {}", userId);

            // 验证用户是否存在
            TUserWeixin user = userWeixinService.getById(userId);
            if (user == null) {
                return ResponseEntity.ok(Map.of(
                        "code", 400,
                        "message", "用户不存在"
                ));
            }

            // 获取用户绑定的房屋ID
            Long houseId = user.getHouseId();
            if (houseId == null) {
                return ResponseEntity.ok(Map.of(
                        "code", 400,
                        "message", "用户未绑定房屋信息"
                ));
            }

            // 获取故障统计信息
            Map<String, Object> statistics = faultService.getFaultStatisticsByHouseId(houseId);

            return ResponseEntity.ok(Map.of(
                    "code", 200,
                    "message", "获取故障统计信息成功",
                    "data", statistics
            ));

        } catch (Exception e) {
            log.error("获取故障统计信息失败", e);
            return ResponseEntity.ok(Map.of(
                    "code", 400,
                    "message", "获取故障统计信息失败：" + e.getMessage()
            ));
        }
    }

    /**
     * 获取简化的账单信息接口
     * 根据用热状态返回不同的费用信息，严格按照账单表数据
     * 不区分停供等复杂场景，仅根据房屋用热状态和账单数据返回
     *
     * 业务逻辑：
     * 1. 根据房屋的用热状态判别费用类型：
     *    - 用热状态(is_heating=1)：显示"用热费"，金额为账单表的total_amount
     *    - 不用热状态(is_heating=0)：显示"管网维护费"，金额为最低缴费金额
     * 2. 欠费金额直接从账单表的overdue_amount字段获取
     * 3. 根据缴费状态判别显示内容：
     *    - 未缴费：显示用热费、欠费金额、应缴费
     *    - 已缴费：显示用热费、欠费金额、应缴费、实际缴费
     *
     * @param request 简化账单信息请求
     * @return 简化账单信息响应
     */
    @PostMapping("/bill/simple-info")
    public ResponseEntity<Map<String, Object>> getSimpleBillInfo(@RequestBody SimpleBillInfoRequest request) {
        try {
            log.info("收到获取简化账单信息请求: {}", request);

            // 参数验证
            if (request.getHouseId() == null) {
                return ResponseEntity.badRequest().body(Map.of(
                        "code", 400,
                        "message", "房屋ID不能为空"
                ));
            }

            // 调用服务层获取账单信息
            SimpleBillInfoResponse response = billService.getSimpleBillInfo(request);

            log.info("简化账单信息获取完成: code={}, message={}", response.getCode(), response.getMessage());

            // 转换为统一的响应格式
            Map<String, Object> result = new HashMap<>();
            result.put("code", response.getCode());
            result.put("message", response.getMessage());
            result.put("data", response.getData());

            return ResponseEntity.ok(result);

        } catch (Exception e) {
            log.error("获取简化账单信息失败", e);
            return ResponseEntity.ok(Map.of(
                    "code", 500,
                    "message", "获取账单信息失败：" + e.getMessage()
            ));
        }
    }
}





















